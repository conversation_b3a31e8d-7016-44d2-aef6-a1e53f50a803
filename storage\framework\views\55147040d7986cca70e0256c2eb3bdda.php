<div class="card mb-4">
    <div class="card-header header-elements">
        <h5 class="mb-0">Task Comments</h5>

        <div class="card-header-elements ms-auto">
            <?php if(($task->users->contains(auth()->user()->id) && !is_null($hasUnderstood)) || $task->creator_id == auth()->user()->id): ?>
                <button type="button" class="btn btn-primary btn-xs" title="Create Comment" data-bs-toggle="collapse" data-bs-target="#taskComment" aria-expanded="false" aria-controls="taskComment">
                    <span class="tf-icon ti ti-message-circle ti-xs me-1"></span>
                    Comment
                </button>
            <?php endif; ?>
        </div>
    </div>
    <!-- Account -->
    <div class="card-body">
        <?php if(($task->users->contains(auth()->user()->id) && !is_null($hasUnderstood)) || $task->creator_id == auth()->user()->id): ?>
            <div class="row">
                <div class="col-md-12">
                    <form action="<?php echo e(route('administration.task.comment.store', ['task' => $task])); ?>" method="post" enctype="multipart/form-data" autocomplete="off" id="taskCommentForm">
                        <?php echo csrf_field(); ?>
                        <div class="collapse show" id="taskComment">
                            <div class="row">
                                <div class="mb-3 col-md-12">
                                    <div name="comment" id="taskCommentEditor"><?php echo old('comment'); ?></div>
                                    <textarea class="d-none" name="comment" id="commentInput"><?php echo e(old('comment')); ?></textarea>
                                    <?php $__errorArgs = ['comment'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <b class="text-danger"><?php echo e($message); ?></b>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                                <div class="col-md-12 mb-1">
                                    <input type="file" id="files[]" name="files[]" value="<?php echo e(old('files[]')); ?>" placeholder="<?php echo e(__('Task Comment Files')); ?>" class="form-control <?php $__errorArgs = ['files[]'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" multiple/>
                                    <?php $__errorArgs = ['files[]'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <b class="text-danger"><i class="ti ti-info-circle mr-1"></i><?php echo e($message); ?></b>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                                <div class="col-md-12">
                                    <button type="submit" class="btn btn-primary btn-sm btn-block mt-2 mb-3">
                                        <i class="ti ti-check"></i>
                                        Submit Comment
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-md-12 comments">
                <table class="table" style="border-spacing: 0 15px; border-collapse: separate;">
                    <tbody>
                        <?php
                            $senderColor = 'background-color: #f0676714 !important; border: 1px solid #f067675c !important;';
                            $receiverColor = 'background-color: #7367f014 !important; border: 1px solid #7367f05c !important;';
                        ?>
                        <?php $__currentLoopData = $task->comments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $comment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr class="mb-3 rounded pt-3" style="<?php echo e($comment->commenter->id == auth()->user()->id ? $senderColor : $receiverColor); ?>">
                                <td class="border-0 border-bottom-0">
                                    <div class="d-flex justify-content-between align-items-center user-name">
                                        <?php echo show_user_name_and_avatar($comment->commenter, name: null); ?>

                                        <small class="date-time text-muted"><?php echo e(date_time_ago($comment->created_at)); ?></small>
                                    </div>
                                    <div class="d-flex mt-2">
                                        <div class="d-block"><?php echo $comment->comment; ?></div>
                                    </div>

                                    <?php if($comment->files->count() > 0): ?>
                                        <div class="d-flex flex-wrap gap-2 pt-1 mb-3">
                                            <?php $__currentLoopData = $comment->files; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $commentFile): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <?php if(in_array($commentFile->mime_type, ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'])): ?>
                                                    <div class="comment-image-container" title="Click to view <?php echo e($commentFile->original_name); ?>">
                                                        <a href="<?php echo e(file_media_download($commentFile)); ?>" data-lightbox="comment-images" data-title="<?php echo e($commentFile->original_name); ?>">
                                                            <img src="<?php echo e(file_media_download($commentFile)); ?>" alt="<?php echo e($commentFile->original_name); ?>" class="img-fluid img-thumbnail" style="width: 150px; height: 100px; object-fit: cover;">
                                                        </a>
                                                    </div>
                                                <?php else: ?>
                                                    <div class="file-thumbnail-container" title="Click to Download <?php echo e($commentFile->original_name); ?>">
                                                        <a href="<?php echo e(file_media_download($commentFile)); ?>" target="_blank" class="text-decoration-none">
                                                            <div class="d-flex flex-column align-items-center">
                                                                <i class="ti ti-file-download fs-2 mb-2 text-primary"></i>
                                                                <span class="file-name text-center small fw-medium">
                                                                    <?php echo e(show_content($commentFile->original_name, 15)); ?>

                                                                </span>
                                                                <small class="text-muted"><?php echo e(strtoupper(pathinfo($commentFile->original_name, PATHINFO_EXTENSION))); ?></small>
                                                            </div>
                                                        </a>
                                                    </div>
                                                <?php endif; ?>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    <?php endif; ?>

                                    <div class="d-flex mt-2">
                                        <form action="<?php echo e(route('administration.task.comment.store', ['task' => $task])); ?>" method="post" enctype="multipart/form-data" autocomplete="off" id="taskCommentReplyForm">
                                            <?php echo csrf_field(); ?>
                                            <div class="collapse" id="taskCommentReply">
                                                <div class="row">
                                                    <div class="mb-3 col-md-12">
                                                        <div name="comment" id="taskCommentReplyEditor"><?php echo old('comment'); ?></div>
                                                        <textarea class="d-none" name="comment" id="commentReplyInput"><?php echo e(old('comment')); ?></textarea>
                                                        <?php $__errorArgs = ['comment'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <b class="text-danger"><?php echo e($message); ?></b>
                                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                    </div>
                                                    <div class="col-md-12 mb-1">
                                                        <input type="file" id="files[]" name="files[]" value="<?php echo e(old('files[]')); ?>" placeholder="<?php echo e(__('Task Comment Files')); ?>" class="form-control <?php $__errorArgs = ['files[]'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" multiple/>
                                                        <?php $__errorArgs = ['files[]'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <b class="text-danger"><i class="ti ti-info-circle mr-1"></i><?php echo e($message); ?></b>
                                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                    </div>
                                                    <div class="col-md-12">
                                                        <button type="submit" class="btn btn-primary btn-sm btn-block mt-2 mb-3">
                                                            <i class="ti ti-check"></i>
                                                            Submit Reply
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </form>
                                    </div>

                                    <div class="d-flex justify-content-end">
                                        <a href="javascript:void(0);" class="btn btn-sm btn-icon btn-primary" title="Reply"  data-bs-toggle="collapse" data-bs-target="#taskCommentReply" aria-expanded="false" aria-controls="taskComment">
                                            <i class="ti ti-arrow-back-up"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>


<?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/task/includes/task_comments.blade.php ENDPATH**/ ?>