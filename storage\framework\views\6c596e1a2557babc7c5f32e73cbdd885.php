<!-- Settings -->
<li class="menu-header small text-uppercase">
    <span class="menu-header-text"><?php echo e(__('Settings')); ?></span>
</li>

<!-- System Settings -->
<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['App Setting Read', 'Weekend Read', 'Holiday Read'])): ?>
    <li class="menu-item <?php echo e(request()->is('settings/system*') ? 'active open' : ''); ?>">
        <a href="javascript:void(0);" class="menu-link menu-toggle">
            <i class="menu-icon tf-icons ti ti-settings"></i>
            <div data-i18n="System Settings"><?php echo e(__('System Settings')); ?></div>
        </a>
        <ul class="menu-sub">
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['App Setting Update', 'App Setting Read'])): ?>
                <li class="menu-item <?php echo e(request()->is('settings/system/app_setting*') ? 'active open' : ''); ?>">
                    <a href="javascript:void(0);" class="menu-link menu-toggle">
                        <div data-i18n="App Setting"><?php echo e(__('App Settings')); ?></div>
                    </a>
                    <ul class="menu-sub">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('App Setting Update')): ?>
                            <li class="menu-item <?php echo e(request()->is('settings/system/app_setting/restrictions*') ? 'active' : ''); ?>">
                                <a href="<?php echo e(route('administration.settings.system.app_setting.restriction.index')); ?>" class="menu-link">
                                    <div data-i18n="Restrictions"><?php echo e(__('Restrictions')); ?></div>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('Weekend Read')): ?>
                <li class="menu-item <?php echo e(request()->is('settings/system/weekend*') ? 'active' : ''); ?>">
                    <a href="<?php echo e(route('administration.settings.system.weekend.index')); ?>" class="menu-link"><?php echo e(__('Weekends')); ?></a>
                </li>
            <?php endif; ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('Holiday Read')): ?>
                <li class="menu-item <?php echo e(request()->is('settings/system/holiday*') ? 'active' : ''); ?>">
                    <a href="<?php echo e(route('administration.settings.system.holiday.index')); ?>" class="menu-link"><?php echo e(__('Holidays')); ?></a>
                </li>
            <?php endif; ?>
        </ul>
    </li>
<?php endif; ?>

<!-- User Management -->
<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['User Create', 'User Read'])): ?>
    <li class="menu-item <?php echo e(request()->is('settings/user*') ? 'active open' : ''); ?>">
        <a href="javascript:void(0);" class="menu-link menu-toggle">
            <i class="menu-icon tf-icons ti ti-user-shield"></i>
            <div data-i18n="User Management"><?php echo e(__('User Management')); ?></div>
        </a>
        <ul class="menu-sub">
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('User Read')): ?>
                <li class="menu-item <?php echo e(request()->is('settings/user/all*') ? 'active' : ''); ?>">
                    <a href="<?php echo e(route('administration.settings.user.index')); ?>" class="menu-link"><?php echo e(__('All Users')); ?></a>
                </li>
            <?php endif; ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('User Create')): ?>
                <li class="menu-item <?php echo e(request()->is('settings/user/barcode*') ? 'active' : ''); ?>">
                    <a href="<?php echo e(route('administration.settings.user.barcode.all')); ?>" class="menu-link"><?php echo e(__('All Barcodes')); ?></a>
                </li>
            <?php endif; ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('User Create')): ?>
                <li class="menu-item <?php echo e(request()->is('settings/user/create*') ? 'active' : ''); ?>">
                    <a href="<?php echo e(route('administration.settings.user.create')); ?>" class="menu-link"><?php echo e(__('Create New User')); ?></a>
                </li>
            <?php endif; ?>
        </ul>
    </li>
<?php endif; ?>

<!-- Role & Permission -->
<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Permission Create', 'Permission Read', 'Role Create', 'Role Read'])): ?>
    <li class="menu-item <?php echo e(request()->is('settings/rolepermission*') ? 'active open' : ''); ?>">
        <a href="javascript:void(0);" class="menu-link menu-toggle">
            <i class="menu-icon tf-icons ti ti-lock"></i>
            <div data-i18n="Role & Permission"><?php echo e(__('Role & Permission')); ?></div>
        </a>
        <ul class="menu-sub">
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Role Create', 'Role Read'])): ?>
                <li class="menu-item <?php echo e(request()->is('settings/rolepermission/role*') ? 'active open' : ''); ?>">
                    <a href="javascript:void(0);" class="menu-link menu-toggle">
                        <div data-i18n="Role"><?php echo e(__('Roles')); ?></div>
                    </a>
                    <ul class="menu-sub">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('Role Read')): ?>
                            <li class="menu-item <?php echo e(request()->is('settings/rolepermission/role/all*') ? 'active' : ''); ?>">
                                <a href="<?php echo e(route('administration.settings.rolepermission.role.index')); ?>" class="menu-link">
                                    <div data-i18n="All Roles"><?php echo e(__('All Roles')); ?></div>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('Role Create')): ?>
                            <li class="menu-item <?php echo e(request()->is('settings/rolepermission/role/create') ? 'active' : ''); ?>">
                                <a href="<?php echo e(route('administration.settings.rolepermission.role.create')); ?>" class="menu-link">
                                    <div data-i18n="Create Role"><?php echo e(__('Create Role')); ?></div>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Permission Create', 'Permission Read'])): ?>
                <li class="menu-item <?php echo e(request()->is('settings/rolepermission/permission*') ? 'active open' : ''); ?>">
                    <a href="javascript:void(0);" class="menu-link menu-toggle">
                        <div data-i18n="Permissions"><?php echo e(__('Permissions')); ?></div>
                    </a>
                    <ul class="menu-sub">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('Permission Read')): ?>
                            <li class="menu-item <?php echo e(request()->is('settings/rolepermission/permission/all*') ? 'active' : ''); ?>">
                                <a href="<?php echo e(route('administration.settings.rolepermission.permission.index')); ?>" class="menu-link">
                                    <div data-i18n="All Permission"><?php echo e(__('All Permissions')); ?></div>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('Permission Create')): ?>
                            <li class="menu-item <?php echo e(request()->is('settings/rolepermission/permission/create*') ? 'active' : ''); ?>">
                                <a href="<?php echo e(route('administration.settings.rolepermission.permission.create')); ?>" class="menu-link">
                                    <div data-i18n="Create Permission"><?php echo e(__('Create Permission')); ?></div>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>
        </ul>
    </li>
<?php endif; ?>

<!-- Shortcuts -->
<li class="menu-item <?php echo e(request()->is('shortcut*') ? 'active open' : ''); ?>">
    <a href="javascript:void(0);" class="menu-link menu-toggle">
        <i class="menu-icon tf-icons ti ti-share-3"></i>
        <div data-i18n="Shortcuts"><?php echo e(__('Shortcuts')); ?></div>
    </a>
    <ul class="menu-sub">
        <li class="menu-item <?php echo e(request()->is('shortcut/all*') ? 'active' : ''); ?>">
            <a href="<?php echo e(route('administration.shortcut.index')); ?>" class="menu-link"><?php echo e(__('My Shortcuts')); ?></a>
        </li>
        <li class="menu-item <?php echo e(request()->is('shortcut/create*') ? 'active' : ''); ?>">
            <a href="<?php echo e(route('administration.shortcut.create')); ?>" class="menu-link"><?php echo e(__('Add Shortcut')); ?></a>
        </li>
    </ul>
</li>
<?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/layouts/administration/partials/menus/settings.blade.php ENDPATH**/ ?>