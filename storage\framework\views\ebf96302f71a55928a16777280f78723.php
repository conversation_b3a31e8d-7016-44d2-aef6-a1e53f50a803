<?php $__env->startSection('meta_tags'); ?>
    

<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_title', __('IT Ticket')); ?>

<?php $__env->startSection('css_links'); ?>
    
    <!-- DataTables css -->
    <link href="<?php echo e(asset('assets/css/custom_css/datatables/dataTables.bootstrap4.min.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('assets/css/custom_css/datatables/datatable.css')); ?>" rel="stylesheet" type="text/css" />

    
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/select2/select2.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/bootstrap-select/bootstrap-select.css')); ?>" />

    
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/bootstrap-datepicker/bootstrap-datepicker.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/bootstrap-daterangepicker/bootstrap-daterangepicker.css')); ?>" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_css'); ?>
    
    <style>
    /* Custom CSS Here */
    </style>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('page_name'); ?>
    <b class="text-uppercase"><?php echo e(__('All IT Tickets')); ?></b>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><?php echo e(__('IT Ticket')); ?></li>
    <li class="breadcrumb-item active"><?php echo e(__('All IT Tickets')); ?></li>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>

<!-- Start row -->
<div class="row justify-content-center">
    <div class="col-md-12">
        <form action="<?php echo e(route('administration.ticket.it_ticket.index')); ?>" method="get" autocomplete="off">
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="mb-3 col-md-4">
                            <label for="solved_by" class="form-label"><?php echo e(__('Select Solver')); ?></label>
                            <select name="solved_by" id="solved_by" class="select2 form-select <?php $__errorArgs = ['solved_by'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" data-allow-clear="true">
                                <option value="" <?php echo e(is_null(request()->solved_by) ? 'selected' : ''); ?>><?php echo e(__('Select Solver')); ?></option>
                                <?php $__currentLoopData = $ticketSolvers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $solver): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($solver->id); ?>" <?php echo e($solver->id == request()->solved_by ? 'selected' : ''); ?>>
                                        <?php echo e(get_employee_name($solver)); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['solved_by'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="mb-3 col-md-4">
                            <label for="creator_id" class="form-label"><?php echo e(__('Select Employee')); ?></label>
                            <select name="creator_id" id="creator_id" class="select2 form-select <?php $__errorArgs = ['creator_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" data-allow-clear="true">
                                <option value="" <?php echo e(is_null(request()->creator_id) ? 'selected' : ''); ?>><?php echo e(__('Select Employee')); ?></option>
                                <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($user->id); ?>" <?php echo e($user->id == request()->creator_id ? 'selected' : ''); ?>>
                                        <?php echo e(get_employee_name($user)); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['creator_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="mb-3 col-md-2">
                            <label class="form-label"><?php echo e(__('IT-Tickets Of')); ?></label>
                            <input type="text" name="ticket_month_year" value="<?php echo e(request()->ticket_month_year ?? old('ticket_month_year')); ?>" class="form-control month-year-picker" placeholder="MM yyyy" tabindex="-1"/>
                            <?php $__errorArgs = ['ticket_month_year'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <span class="text-danger"><?php echo e($message); ?></span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="mb-3 col-md-2">
                            <label for="status" class="form-label"><?php echo e(__('Select Status')); ?></label>
                            <select name="status" id="status" class="form-select bootstrap-select w-100 <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"  data-style="btn-default">
                                
                                <option value="" <?php echo e(is_null(request()->status) ? 'selected' : ''); ?>><?php echo e(__('Select status')); ?></option>
                                <option value="Pending" <?php echo e(request()->status == 'Pending' ? 'selected' : ''); ?>><?php echo e(__('Pending')); ?></option>
                                <option value="Running" <?php echo e(request()->status == 'Running' ? 'selected' : ''); ?>><?php echo e(__('Running')); ?></option>
                                <option value="Solved" <?php echo e(request()->status == 'Solved' ? 'selected' : ''); ?>><?php echo e(__('Solved')); ?></option>
                                <option value="Canceled" <?php echo e(request()->status == 'Canceled' ? 'selected' : ''); ?>><?php echo e(__('Canceled')); ?></option>
                            </select>
                            <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <div class="col-md-12 text-end">
                        <?php if(request()->solved_by || request()->creator_id || request()->ticket_month_year || request()->status): ?>
                            <a href="<?php echo e(route('administration.ticket.it_ticket.index')); ?>" class="btn btn-danger confirm-warning">
                                <span class="tf-icon ti ti-refresh ti-xs me-1"></span>
                                <?php echo e(__('Reset Filters')); ?>

                            </a>
                        <?php endif; ?>
                        <button type="submit" name="filter_tickets" value="true" class="btn btn-primary">
                            <span class="tf-icon ti ti-filter ti-xs me-1"></span>
                            <?php echo e(__('Filter Tickets')); ?>

                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header header-elements">
                <h5 class="mb-0">
                    <?php if(request()->has('filter_tickets')): ?>
                        <?php if(request()->filled('status')): ?>
                            <?php echo e(__(request()->status)); ?>

                        <?php endif; ?>
                        IT Tickets
                        <?php if(request()->filled('ticket_month_year')): ?>
                            of <?php echo e(request()->ticket_month_year); ?>

                        <?php endif; ?>
                        <?php if(request()->filled('solved_by') && $ticketSolvers->where('id', request()->solved_by)->first()): ?>
                            solved by <?php echo e(get_employee_name($ticketSolvers->where('id', request()->solved_by)->first())); ?>

                        <?php endif; ?>
                        <?php if(request()->filled('creator_id') && $users->where('id', request()->creator_id)->first()): ?>
                            created by <?php echo e(get_employee_name($users->where('id', request()->creator_id)->first())); ?>

                        <?php endif; ?>
                    <?php else: ?>
                        All IT Tickets of <?php echo e(date('F Y')); ?>

                    <?php endif; ?>
                </h5>

                <div class="card-header-elements ms-auto">
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('IT Ticket Create')): ?>
                        <a href="<?php echo e(route('administration.ticket.it_ticket.create')); ?>" class="btn btn-sm btn-primary">
                            <span class="tf-icon ti ti-plus ti-xs me-1"></span>
                            Arise New Ticket
                        </a>
                    <?php endif; ?>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive-md table-responsive-sm w-100">
                    <table class="table data-table table-bordered">
                        <thead>
                            <tr>
                                <th>Sl.</th>
                                <th>Creator</th>
                                <th>Title</th>
                                <th>Created At</th>
                                <th>Status</th>
                                <th class="text-center">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $itTickets; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $ticket): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <th>#<?php echo e(serial($itTickets, $key)); ?></th>
                                    <td>
                                        <?php echo show_user_name_and_avatar($ticket->creator, name: false); ?>

                                    </td>
                                    <td>
                                        <b title="<?php echo e($ticket->title); ?>"><?php echo e(show_content($ticket->title, 30)); ?></b>
                                        <br>
                                        <small class="text-muted"><?php echo show_content($ticket->description, 40); ?></small>
                                    </td>
                                    <td>
                                        <b><?php echo e(show_date($ticket->created_at)); ?></b>
                                        <br>
                                        <span>at <b><?php echo e(show_time($ticket->created_at)); ?></b></span>
                                    </td>
                                    <td>
                                        <?php if($ticket->status === 'Pending'): ?>
                                            <span class="badge bg-dark"><?php echo e(__('Pending')); ?></span>
                                        <?php elseif($ticket->status === 'Running'): ?>
                                            <span class="badge bg-primary"><?php echo e(__('Running')); ?></span>
                                        <?php elseif($ticket->status === 'Solved'): ?>
                                            <span class="badge bg-success"><?php echo e(__('Solved')); ?></span>
                                        <?php else: ?>
                                            <span class="badge bg-danger"><?php echo e(__('Canceled')); ?></span>
                                        <?php endif; ?>
                                        <?php if(isset($ticket->solver)): ?>
                                            <br>
                                            <small title="<?php echo e($ticket->status); ?> By">
                                                <?php echo show_user_name_and_avatar($ticket->solver, avatar: false, name: false, role: false); ?>

                                            </small>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-center">
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('IT Ticket Delete')): ?>
                                            <a href="<?php echo e(route('administration.ticket.it_ticket.destroy', ['it_ticket' => $ticket])); ?>" class="btn btn-sm btn-icon btn-danger confirm-danger" data-bs-toggle="tooltip" title="Delete IT Ticket?">
                                                <i class="text-white ti ti-trash"></i>
                                            </a>
                                        <?php endif; ?>
                                        <?php if($ticket->status === 'Pending' && $ticket->creator_id == auth()->user()->id): ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('IT Ticket Update')): ?>
                                                <a href="<?php echo e(route('administration.ticket.it_ticket.edit', ['it_ticket' => $ticket])); ?>" class="btn btn-sm btn-icon btn-info" data-bs-toggle="tooltip" title="Edit IT Ticket?">
                                                    <i class="text-white ti ti-pencil"></i>
                                                </a>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('IT Ticket Read')): ?>
                                            <a href="<?php echo e(route('administration.ticket.it_ticket.show', ['it_ticket' => $ticket])); ?>" class="btn btn-sm btn-icon btn-primary" data-bs-toggle="tooltip" title="Show Details">
                                                <i class="text-white ti ti-info-hexagon"></i>
                                            </a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- End row -->

<?php $__env->stopSection(); ?>


<?php $__env->startSection('script_links'); ?>
    
    <!-- Datatable js -->
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/jquery.dataTables.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/dataTables.bootstrap4.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/datatable.js')); ?>"></script>

    <script src="<?php echo e(asset('assets/js/form-layouts.js')); ?>"></script>

    <script src="<?php echo e(asset('assets/vendor/libs/select2/select2.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/bootstrap-select/bootstrap-select.js')); ?>"></script>

    <script src="<?php echo e(asset('assets/vendor/libs/bootstrap-datepicker/bootstrap-datepicker.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/bootstrap-daterangepicker/bootstrap-daterangepicker.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_script'); ?>
    
    <script>
        // Custom Script Here
        $(document).ready(function () {
            $('.bootstrap-select').each(function() {
                if (!$(this).data('bs.select')) { // Check if it's already initialized
                    $(this).selectpicker();
                }
            });

            $('.month-year-picker').datepicker({
                format: 'MM yyyy',         // Display format to show full month name and year
                minViewMode: 'months',     // Only allow month selection
                todayHighlight: true,
                autoclose: true,
                orientation: 'auto right'
            });
        });
    </script>
<?php $__env->stopSection(); ?>


<?php echo $__env->make('layouts.administration.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/ticket/it_ticket/index.blade.php ENDPATH**/ ?>